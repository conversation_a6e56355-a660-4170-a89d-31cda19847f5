// 吴 - 内容脚本（简化版）
class AdPopupKiller {
    constructor() {
        this.scanInterval = null;
        this.closedCount = 0;

        // 支持所有弹窗的关闭按钮选择器
        this.closeButtonSelectors = [
            // 基础选择器 - 覆盖所有版本
            'svg[data-testid="beast-core-modal-icon-close"]',
            'svg[data-testid="beast-core-icon-close"]',
            // 类名选择器 - 5-118-0版本
            '.MDL_headerCloseIcon_5-118-0',
            '.ICN_outerWrapper_5-118-0',
            '.ICN_svgIcon_5-118-0',
            // 类名选择器 - 5-119-0版本
            '.MDL_headerCloseIcon_5-119-0',
            '.ICN_outerWrapper_5-119-0',
            '.ICN_svgIcon_5-119-0',
            // modal-content变体
            '.modal-content_closeIcon__2C8Fb',
            '.modal-content_closeIcon__Gw9DZ',
            '.modal-content_closeIcon__7mkdd'
        ];

        this.init();
    }
    
    init() {
        console.log('吴已启动');
        this.startScanning();
    }
    
    startScanning() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }

        this.scanInterval = setInterval(() => {
            this.scanAndClosePopups();
        }, 500); // 每0.5秒扫描一次
    }
    
    scanAndClosePopups() {
        try {
            // 查找并关闭弹窗
            const closeButtons = this.findCloseButtons();

            closeButtons.forEach(button => {
                if (this.isVisibleAndClickable(button)) {
                    this.clickCloseButton(button);
                }
            });

        } catch (error) {
            console.error('扫描弹窗时出错:', error);
        }
    }
    
    findCloseButtons() {
        const buttons = [];

        // 使用选择器查找关闭按钮
        this.closeButtonSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (!buttons.includes(element)) {
                        buttons.push(element);
                    }
                });
            } catch (e) {
                // 忽略无效的选择器
            }
        });

        return buttons;
    }


    
    isVisibleAndClickable(element) {
        if (!element) return false;

        // 检查元素是否可见
        const style = window.getComputedStyle(element);
        if (style.display === 'none' ||
            style.visibility === 'hidden' ||
            style.opacity === '0') {
            return false;
        }

        // 检查元素是否在视口内
        const rect = element.getBoundingClientRect();
        if (rect.width === 0 || rect.height === 0) {
            return false;
        }

        return true;
    }
    

    
    clickCloseButton(button) {
        try {
            console.log('发现弹窗关闭按钮，正在点击...', button);

            // 直接点击
            button.click();

            this.closedCount++;
            console.log(`已关闭 ${this.closedCount} 个弹窗`);

        } catch (error) {
            console.error('点击关闭按钮时出错:', error);
        }
    }

    destroy() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }
    }
}

// 创建实例
const adPopupKiller = new AdPopupKiller();

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    adPopupKiller.destroy();
});
